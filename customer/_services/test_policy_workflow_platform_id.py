#!/usr/bin/env python3
"""
Test script to verify the platformId lookup functionality in policy_workflow_service.py

This script demonstrates how the updated service handles platformId parameters
and queries the customer_customerplatformidentity table to extract the required fields.
"""

import os
import sys
import django

# Add the project root to the Python path
sys.path.append('/Users/<USER>/Developer/Salmate')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'salmate.settings')
django.setup()

from customer._services.policy_workflow_service import PolicyWorkflowService
from customer.models import Customer, CustomerPlatformIdentity
from django.contrib.auth.models import User


def test_platform_identity_lookup():
    """Test the platformId lookup functionality"""
    print("=== Testing Platform Identity Lookup ===")
    
    try:
        # Get a sample platform identity from the database
        platform_identity = CustomerPlatformIdentity.objects.filter(is_active=True).first()
        
        if not platform_identity:
            print("No active platform identities found in database")
            return
        
        platform_id = platform_identity.id
        print(f"Testing with platform_id: {platform_id}")
        
        # Test the new get_platform_identity_data method
        platform_data = PolicyWorkflowService.get_platform_identity_data(platform_id)
        
        print("Platform Identity Data Retrieved:")
        print(f"  - platform_user_id: {platform_data['platform_user_id']}")
        print(f"  - channel_id: {platform_data['channel_id']}")
        print(f"  - platform: {platform_data['platform']}")
        print(f"  - customer_id: {platform_data['customer_id']}")
        print(f"  - display_name: {platform_data['display_name']}")
        print(f"  - provider_id: {platform_data['provider_id']}")
        print(f"  - provider_name: {platform_data['provider_name']}")
        print(f"  - channel_name: {platform_data['channel_name']}")
        
        print("\n✅ Platform identity lookup successful!")
        
        return platform_data
        
    except Exception as e:
        print(f"❌ Error during platform identity lookup: {str(e)}")
        return None


def test_workflow_execution_by_platform_id():
    """Test workflow execution using platformId"""
    print("\n=== Testing Workflow Execution by Platform ID ===")
    
    try:
        # Get a sample platform identity
        platform_identity = CustomerPlatformIdentity.objects.filter(is_active=True).first()
        
        if not platform_identity:
            print("No active platform identities found in database")
            return
        
        platform_id = platform_identity.id
        print(f"Testing workflow execution with platform_id: {platform_id}")
        
        # Get a test user
        user = User.objects.filter(is_active=True).first()
        if not user:
            print("No active users found in database")
            return
        
        print(f"Using user: {user.username}")
        
        # Test the new workflow execution method
        print("Executing policy list workflow by platform ID...")
        
        # Note: This will actually try to execute the workflow, so we'll catch any errors
        try:
            result = PolicyWorkflowService.execute_policy_list_workflow_by_platform_id(
                platform_id=platform_id,
                user=user
            )
            print("✅ Workflow execution successful!")
            print(f"Result keys: {list(result.keys())}")
            
        except Exception as workflow_error:
            print(f"⚠️  Workflow execution failed (expected if TPA service not available): {str(workflow_error)}")
            print("This is normal if the TPA service is not configured or accessible")
        
    except Exception as e:
        print(f"❌ Error during workflow execution test: {str(e)}")


def main():
    """Main test function"""
    print("Testing Policy Workflow Service Platform ID Implementation")
    print("=" * 60)
    
    # Test 1: Platform identity lookup
    platform_data = test_platform_identity_lookup()
    
    # Test 2: Workflow execution (if platform data was retrieved successfully)
    if platform_data:
        test_workflow_execution_by_platform_id()
    
    print("\n" + "=" * 60)
    print("Test completed!")


if __name__ == "__main__":
    main()
