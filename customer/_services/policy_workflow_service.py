import uuid
import time
import logging
import json
import os
import re
from datetime import timed<PERSON><PERSON>
from typing import Dict, Any, Optional
from django.utils import timezone
from django.db import transaction
from django.contrib.auth.models import User

from customer.models import Customer, CustomerPlatformIdentity, CustomerPolicyWorkflowCache, CustomerPolicyWorkflowAuditLog
from .policy_tpa_service import TPAApiService
from customer.exceptions import PolicyWorkflowError, CustomerDataError

logger = logging.getLogger('django.customer_policy_crm')

# Global configuration constants - moved to top for maintainability
WORKFLOW_CONFIG_FILES = {
    'POLICY_LIST': 'policy-list-workflow.json',
    'POLICY_DETAILS': 'policy-details-workflow.json'
}

# Workflow types
WORKFLOW_TYPE_POLICY_LIST = 'POLICY_LIST'
WORKFLOW_TYPE_POLICY_DETAILS = 'POLICY_DETAILS'

# Template pattern for variable resolution
TEMPLATE_VARIABLE_PATTERN = r'\{\{([^}]+)\}\}'

# Default retry settings
DEFAULT_RETRY_DELAY_SECONDS = 3
DEFAULT_MAX_RETRIES = 1

# Cache settings
DEFAULT_CACHE_DURATION_MINUTES = 1 # TODO: Change to 60-240 (1-4 hours) in production 

# Platform settings
DEFAULT_PLATFORM = 'LINE'

# Configuration modes
CONFIG_MODE_FIXED = 'fixed'
CONFIG_MODE_DATABASE = 'database'

# JSONPath constants
JSONPATH_ROOT = '$'
JSONPATH_PREFIX = '$.'
JSONPATH_ARRAY_WILDCARD = '[*]'

# Validation rules
VALIDATION_CITIZEN_ID_STATUS = "Status == '1'" 
VALIDATION_REGISTRATION_STATUS = "Status == 'YES'"

# Status values
STATUS_CITIZEN_ID_VALID = '1'
STATUS_REGISTRATION_YES = 'YES'

# Step handler names
STEP_GET_BEARER_TOKEN = 'get_bearer_token'
STEP_VERIFY_CITIZEN_ID = 'verify_citizen_id'
STEP_VERIFY_REGISTRATION = 'verify_registration'
STEP_FETCH_POLICY_LIST = 'fetch_policy_list'
STEP_FETCH_POLICY_DETAILS = 'fetch_policy_details'

# Response field names
FIELD_LIST_OF_SEARCH_CITIZEN_ID = 'ListOfSearchCitizenID'
FIELD_LIST_OF_CHECK_REGISTER = 'ListOfCheckRegister'
FIELD_LIST_OF_POLICY_LIST_SOCIAL = 'ListOfPolicyListSocial'
FIELD_LIST_OF_POL_DET = 'ListOfPolDet'
FIELD_LIST_OF_POL_CLAIM = 'ListOfPolClaim'
FIELD_STATUS = 'Status'
FIELD_MEMBER_CODE = 'MemberCode'

# Context keys
CONTEXT_STEP_DATA = 'step_data'
CONTEXT_STEP_RESULTS = 'step_results'
CONTEXT_TPA_CALLS = 'tpa_calls'
CONTEXT_TPA_TIME = 'tpa_time'
CONTEXT_CUSTOMER = 'customer'
CONTEXT_MEMBER_CODE = 'member_code'
CONTEXT_PROCESSED_DATA = 'processed_data'
CONTEXT_EXECUTION_ID = 'execution_id'

# Step result keys
RESULT_SUCCESS = 'success'
RESULT_ERROR = 'error'
RESULT_TIME_MS = 'time_ms'
RESULT_DATA = 'data'
RESULT_EXTRACTED_DATA = 'extracted_data'
RESULT_ATTEMPT = 'attempt'

# Configuration keys
CONFIG_KEY_CONFIG = 'config'
CONFIG_KEY_MODE = 'mode'
CONFIG_KEY_FIXED_VALUES = 'fixed_values'
CONFIG_KEY_STEPS = 'steps'
CONFIG_KEY_ID = 'id'
CONFIG_KEY_NAME = 'name'
CONFIG_KEY_RETRY = 'retry'
CONFIG_KEY_RETRY_DELAY_SECONDS = 'retry_delay_seconds'
CONFIG_KEY_EXTRACT = 'extract'
CONFIG_KEY_VALIDATE = 'validate'
CONFIG_KEY_REQUEST = 'request'
CONFIG_KEY_HEADERS = 'headers'
CONFIG_KEY_ENDPOINT = 'endpoint'
CONFIG_KEY_METHOD = 'method'

# Data keys for workflow results
DATA_KEY_POLICIES = 'policies'
DATA_KEY_POLICY_DETAILS = 'policy_details'
DATA_KEY_CLAIMS_DATA = 'claims_data'

# Error messages
ERROR_UNKNOWN_WORKFLOW_TYPE = "Unknown workflow type: {}"
ERROR_UNKNOWN_STEP_HANDLER = "Unknown step handler: {}"
ERROR_MAX_RETRIES_EXCEEDED = "Maximum retries exceeded"
ERROR_STEP_FAILED = "Step {} ({}) failed: {}"
ERROR_UNKNOWN_ERROR = "Unknown error"


class WorkflowConfigLoader:
    """Loads and manages workflow configurations from JSON files"""

    @staticmethod
    def load_workflow_config(workflow_type: str) -> Dict[str, Any]:
        """Load workflow configuration from JSON file"""
        logger.debug(f"WorkflowConfigLoader.load_workflow_config: Loading config for workflow_type={workflow_type}")

        if workflow_type not in WORKFLOW_CONFIG_FILES:
            logger.error(f"WorkflowConfigLoader.load_workflow_config: Unknown workflow type: {workflow_type}")
            raise PolicyWorkflowError(ERROR_UNKNOWN_WORKFLOW_TYPE.format(workflow_type))

        config_file = WORKFLOW_CONFIG_FILES[workflow_type]
        config_path = os.path.join(os.path.dirname(__file__), config_file)
        logger.debug(f"WorkflowConfigLoader.load_workflow_config: Loading config from path: {config_path}")

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            logger.info(f"WorkflowConfigLoader.load_workflow_config: Successfully loaded config for {workflow_type} with {len(config.get('steps', []))} steps")
            return config
        except FileNotFoundError:
            logger.error(f"WorkflowConfigLoader.load_workflow_config: Configuration file not found: {config_file}")
            raise PolicyWorkflowError(f"Workflow configuration file not found: {config_file}")
        except json.JSONDecodeError as e:
            logger.error(f"WorkflowConfigLoader.load_workflow_config: Invalid JSON in configuration file {config_file}: {str(e)}")
            raise PolicyWorkflowError(f"Invalid JSON in workflow configuration: {str(e)}")


class WorkflowTemplateResolver:
    """Resolves template variables in workflow configurations"""

    @staticmethod
    def resolve_template_variables(template: str, context: Dict[str, Any]) -> str:
        """Resolve template variables like {{variable_name}} in strings"""
        if not isinstance(template, str):
            return template

        # Find all template variables in the format {{variable_name}}
        matches = re.findall(TEMPLATE_VARIABLE_PATTERN, template)
        logger.debug(f"WorkflowTemplateResolver.resolve_template_variables: Found {len(matches)} template variables in: {template[:100]}...")

        resolved = template
        for match in matches:
            variable_name = match.strip()
            if variable_name in context:
                old_value = f'{{{{{variable_name}}}}}'
                new_value = str(context[variable_name])
                resolved = resolved.replace(old_value, new_value)
                logger.debug(f"WorkflowTemplateResolver.resolve_template_variables: Resolved {old_value} -> {new_value}")
            else:
                logger.warning(f"WorkflowTemplateResolver.resolve_template_variables: Template variable '{variable_name}' not found in context")

        return resolved

    @staticmethod
    def resolve_request_data(request_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Resolve template variables in request data"""
        resolved = {}
        for key, value in request_data.items():
            if isinstance(value, str):
                resolved[key] = WorkflowTemplateResolver.resolve_template_variables(value, context)
            else:
                resolved[key] = value
        return resolved

    @staticmethod
    def resolve_headers(headers: Dict[str, str], context: Dict[str, Any]) -> Dict[str, str]:
        """Resolve template variables in headers"""
        resolved = {}
        for key, value in headers.items():
            resolved[key] = WorkflowTemplateResolver.resolve_template_variables(value, context)
        return resolved


class WorkflowDataExtractor:
    """Extracts data from API responses using JSONPath-like expressions"""

    @staticmethod
    def extract_data(response: Any, extract_config: Dict[str, str]) -> Dict[str, Any]:
        """Extract data from response using extraction configuration"""
        logger.debug(f"WorkflowDataExtractor.extract_data: Starting data extraction with {len(extract_config) if extract_config else 0} extraction rules")

        if not extract_config:
            logger.debug("WorkflowDataExtractor.extract_data: No extraction configuration provided, returning empty dict")
            return {}

        extracted = {}
        for key, path in extract_config.items():
            try:
                logger.debug(f"WorkflowDataExtractor.extract_data: Extracting '{key}' using path '{path}'")
                value = WorkflowDataExtractor._extract_by_path(response, path)
                extracted[key] = value
                logger.debug(f"WorkflowDataExtractor.extract_data: Successfully extracted '{key}': {type(value).__name__} with length {len(value) if isinstance(value, (list, dict, str)) else 'N/A'}")
            except Exception as e:
                logger.warning(f"WorkflowDataExtractor.extract_data: Failed to extract '{key}' using path '{path}': {str(e)}")
                extracted[key] = None

        logger.info(f"WorkflowDataExtractor.extract_data: Completed extraction, extracted {len(extracted)} fields")
        return extracted

    @staticmethod
    def _extract_by_path(data: Any, path: str) -> Any:
        """Extract data using JSONPath-like syntax"""
        if path == JSONPATH_ROOT:
            return data

        if not path.startswith(JSONPATH_PREFIX):
            return None

        # Remove the '$.' prefix
        path_parts = path[2:].split('.')
        current = data

        for part in path_parts:
            if JSONPATH_ARRAY_WILDCARD in part:
                # Handle array extraction like 'ListOfPolicyListSocial[*].MemberCode'
                array_key = part.replace(JSONPATH_ARRAY_WILDCARD, '')
                if isinstance(current, dict) and array_key in current:
                    array_data = current[array_key]
                    if isinstance(array_data, list):
                        # Get the next part to extract from each array item
                        remaining_parts = path_parts[path_parts.index(part) + 1:]
                        if remaining_parts:
                            # Extract specific field from each array item
                            result = []
                            for item in array_data:
                                item_value = item
                                for remaining_part in remaining_parts:
                                    if isinstance(item_value, dict) and remaining_part in item_value:
                                        item_value = item_value[remaining_part]
                                    else:
                                        item_value = None
                                        break
                                if item_value is not None:
                                    result.append(item_value)
                            return result
                        else:
                            return array_data
                return None
            else:
                # Regular field access
                if isinstance(current, dict) and part in current:
                    current = current[part]
                else:
                    return None

        return current


class WorkflowValidator:
    """Validates workflow step results"""

    @staticmethod
    def validate_response(response: Any, validation_rule: str) -> bool:
        """Validate response against validation rule"""
        logger.debug(f"WorkflowValidator.validate_response: Starting validation with rule: {validation_rule}")

        if not validation_rule:
            logger.debug("WorkflowValidator.validate_response: No validation rule provided, returning True")
            return True

        try:
            # Handle citizen ID verification
            if VALIDATION_CITIZEN_ID_STATUS in validation_rule:
                logger.debug("WorkflowValidator.validate_response: Applying citizen ID status validation")
                result = WorkflowValidator._validate_citizen_id_status(response)
                logger.info(f"WorkflowValidator.validate_response: Citizen ID validation result: {result}")
                return result

            # Handle registration verification
            elif VALIDATION_REGISTRATION_STATUS in validation_rule:
                logger.debug("WorkflowValidator.validate_response: Applying registration status validation")
                result = WorkflowValidator._validate_registration_status(response)
                logger.info(f"WorkflowValidator.validate_response: Registration validation result: {result}")
                return result

            else:
                logger.warning(f"WorkflowValidator.validate_response: Unknown validation rule: {validation_rule}")
                return True

        except Exception as e:
            logger.error(f"WorkflowValidator.validate_response: Validation failed with rule '{validation_rule}': {str(e)}")
            return False

    @staticmethod
    def _validate_citizen_id_status(response: Any) -> bool:
        """Validate citizen ID verification response"""
        if not isinstance(response, dict):
            raise ValueError("Response must be a dictionary")

        search_results = response.get(FIELD_LIST_OF_SEARCH_CITIZEN_ID, [])
        if not isinstance(search_results, list) or len(search_results) == 0:
            raise ValueError("No citizen ID search results found")

        first_result = search_results[0]
        if not isinstance(first_result, dict):
            raise ValueError("Invalid search result format")

        status = first_result.get(FIELD_STATUS)
        if status != STATUS_CITIZEN_ID_VALID:
            raise ValueError(f"Citizen ID verification failed: Status = {status}")

        return True

    @staticmethod
    def _validate_registration_status(response: Any) -> bool:
        """Validate registration check response"""
        if not isinstance(response, dict):
            raise ValueError("Response must be a dictionary")

        check_results = response.get(FIELD_LIST_OF_CHECK_REGISTER, [])
        if not isinstance(check_results, list) or len(check_results) == 0:
            raise ValueError("No registration check results found")

        first_result = check_results[0]
        if not isinstance(first_result, dict):
            raise ValueError("Invalid check result format")

        status = first_result.get(FIELD_STATUS)
        if status != STATUS_REGISTRATION_YES:
            raise ValueError(f"Registration verification failed: Status = {status}")

        return True


class GenericWorkflowExecutor:
    """Generic workflow execution engine that processes JSON-defined workflows"""

    def __init__(self, tpa_service: TPAApiService):
        logger.debug("GenericWorkflowExecutor.__init__: Initializing workflow executor")
        self.tpa_service = tpa_service
        self.step_handlers = {
            STEP_GET_BEARER_TOKEN: self._execute_get_bearer_token,
            STEP_VERIFY_CITIZEN_ID: self._execute_verify_citizen_id,
            STEP_VERIFY_REGISTRATION: self._execute_verify_registration,
            STEP_FETCH_POLICY_LIST: self._execute_fetch_policy_list,
            STEP_FETCH_POLICY_DETAILS: self._execute_fetch_policy_details,
        }
        logger.info(f"GenericWorkflowExecutor.__init__: Initialized with {len(self.step_handlers)} step handlers")

    def execute_workflow(self, workflow_type: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a complete workflow based on JSON configuration"""
        execution_id = context.get(CONTEXT_EXECUTION_ID, 'unknown')
        logger.info(f"GenericWorkflowExecutor.execute_workflow: Starting workflow execution - type={workflow_type}, execution_id={execution_id}")

        config = WorkflowConfigLoader.load_workflow_config(workflow_type)

        # Initialize execution context
        execution_context = {
            CONTEXT_STEP_DATA: {},
            CONTEXT_STEP_RESULTS: {},
            CONTEXT_TPA_CALLS: 0,
            CONTEXT_TPA_TIME: 0,
            **context
        }
        logger.debug(f"GenericWorkflowExecutor.execute_workflow: Initialized execution context with {len(context)} context items")

        # Resolve data source (database or fixed values)
        logger.debug("GenericWorkflowExecutor.execute_workflow: Resolving data source")
        self._resolve_data_source(config, execution_context)

        # Execute each step in sequence
        steps = sorted(config[CONFIG_KEY_STEPS], key=lambda x: x[CONFIG_KEY_ID])
        logger.info(f"GenericWorkflowExecutor.execute_workflow: Executing {len(steps)} workflow steps in sequence")

        for i, step in enumerate(steps, 1):
            step_name = step[CONFIG_KEY_NAME]
            step_id = step[CONFIG_KEY_ID]
            logger.info(f"GenericWorkflowExecutor.execute_workflow: Executing step {i}/{len(steps)} - {step_id}:{step_name}")

            step_result = self._execute_step(step, execution_context)

            step_key = f"step_{step_id}_{step_name}"
            execution_context[CONTEXT_STEP_RESULTS][step_key] = step_result

            if not step_result[RESULT_SUCCESS]:
                error_msg = ERROR_STEP_FAILED.format(step_id, step_name, step_result.get(RESULT_ERROR, ERROR_UNKNOWN_ERROR))
                logger.error(f"GenericWorkflowExecutor.execute_workflow: Step failed - {error_msg}")
                raise PolicyWorkflowError(error_msg)

            # Store extracted data for subsequent steps
            if RESULT_EXTRACTED_DATA in step_result:
                extracted_count = len(step_result[RESULT_EXTRACTED_DATA])
                execution_context[CONTEXT_STEP_DATA].update(step_result[RESULT_EXTRACTED_DATA])
                logger.debug(f"GenericWorkflowExecutor.execute_workflow: Step {step_name} extracted {extracted_count} data fields")

        logger.info(f"GenericWorkflowExecutor.execute_workflow: Workflow completed successfully - type={workflow_type}, execution_id={execution_id}, total_tpa_calls={execution_context[CONTEXT_TPA_CALLS]}, total_tpa_time={execution_context[CONTEXT_TPA_TIME]:.2f}ms")
        return execution_context

    def _resolve_data_source(self, config: Dict[str, Any], context: Dict[str, Any]):
        """Resolve data source based on configuration mode"""
        config_mode = config.get(CONFIG_KEY_CONFIG, {}).get(CONFIG_KEY_MODE, CONFIG_MODE_FIXED)

        if config_mode == CONFIG_MODE_FIXED:
            # Use fixed values from configuration
            fixed_values = config.get(CONFIG_KEY_CONFIG, {}).get(CONFIG_KEY_FIXED_VALUES, {})
            context[CONTEXT_STEP_DATA].update(fixed_values)
        elif config_mode == CONFIG_MODE_DATABASE:
            # Use database queries from configuration
            database_config = config.get(CONFIG_KEY_CONFIG, {}).get('database', [])
            customer = context.get(CONTEXT_CUSTOMER)
            customer_id = context.get('customer_id') or (customer.customer_id if customer else None)

            # Get platform_id from context - this is passed from the frontend
            platform_identity = context.get('platform_identity')
            platform_id = context.get('platform_id') or (platform_identity.id if platform_identity else None)

            if customer_id:
                resolved_data = {}
                for db_query in database_config:
                    table_data = self._execute_database_query(db_query, str(customer_id), str(platform_id) if platform_id else None)
                    resolved_data.update(table_data)
                logger.debug(f"GenericWorkflowExecutor._resolve_data_source: Resolved database data: {resolved_data}")
            else:
                resolved_data = {}

            context[CONTEXT_STEP_DATA].update(resolved_data)

        # Add member_code if provided in context
        if CONTEXT_MEMBER_CODE in context:
            context[CONTEXT_STEP_DATA][CONTEXT_MEMBER_CODE] = context[CONTEXT_MEMBER_CODE]

    def _execute_database_query(self, db_config: Dict[str, Any], customer_id: str, platform_id: Optional[str] = None) -> Dict[str, Any]:
        """Execute database query based on configuration"""
        from django.db import connection

        table = db_config['table']
        fields = db_config['fields']
        where_clause = db_config['where']

        # Prepare parameters list based on what's needed in the where clause
        params = []

        # Replace named parameters with Django's placeholder syntax and collect parameter values
        if ':customer_id' in where_clause:
            where_clause = where_clause.replace(':customer_id', '%s')
            params.append(customer_id)

        if ':platform_id' in where_clause:
            where_clause = where_clause.replace(':platform_id', '%s')
            if platform_id is None:
                logger.error(f"GenericWorkflowExecutor._execute_database_query: platform_id required for query but not provided")
                return {}
            params.append(platform_id)

        # Build SQL query
        field_list = ', '.join(fields.values())
        sql = f"SELECT {field_list} FROM {table} WHERE {where_clause}"

        logger.debug(f"GenericWorkflowExecutor._execute_database_query: Executing SQL: {sql} with params: {params}")

        with connection.cursor() as cursor:
            cursor.execute(sql, params)
            row = cursor.fetchone()

            if row:
                # Map database fields to workflow variables
                result = {}
                for workflow_var, db_field in fields.items():
                    field_index = list(fields.values()).index(db_field)
                    result[workflow_var] = row[field_index]
                logger.debug(f"GenericWorkflowExecutor._execute_database_query: Query returned data: {result}")
                return result
            else:
                logger.warning(f"GenericWorkflowExecutor._execute_database_query: No data found for table {table} with params {params}")

        return {}

    def _execute_step(self, step: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a single workflow step with retry logic"""
        step_name = step[CONFIG_KEY_NAME]
        step_id = step[CONFIG_KEY_ID]
        max_retries = step.get(CONFIG_KEY_RETRY, DEFAULT_MAX_RETRIES)
        execution_id = context.get(CONTEXT_EXECUTION_ID, 'unknown')

        logger.debug(f"GenericWorkflowExecutor._execute_step: Starting step execution - step_id={step_id}, step_name={step_name}, max_retries={max_retries}, execution_id={execution_id}")

        for attempt in range(max_retries):
            try:
                logger.debug(f"GenericWorkflowExecutor._execute_step: Attempt {attempt + 1}/{max_retries} for step {step_name}")
                start_time = time.time()

                # Get step handler
                handler = self.step_handlers.get(step_name)
                if not handler:
                    logger.error(f"GenericWorkflowExecutor._execute_step: No handler found for step {step_name}")
                    raise PolicyWorkflowError(ERROR_UNKNOWN_STEP_HANDLER.format(step_name))

                # Execute step
                logger.debug(f"GenericWorkflowExecutor._execute_step: Executing handler for step {step_name}")
                response = handler(step, context)
                execution_time = (time.time() - start_time) * 1000
                logger.info(f"GenericWorkflowExecutor._execute_step: Step {step_name} executed successfully in {execution_time:.2f}ms")

                # Extract data from response
                extracted_data = {}
                if CONFIG_KEY_EXTRACT in step:
                    logger.debug(f"GenericWorkflowExecutor._execute_step: Extracting data from step {step_name} response")
                    extracted_data = WorkflowDataExtractor.extract_data(response, step[CONFIG_KEY_EXTRACT])

                # Validate response if validation rule exists
                if CONFIG_KEY_VALIDATE in step:
                    logger.debug(f"GenericWorkflowExecutor._execute_step: Validating response for step {step_name}")
                    WorkflowValidator.validate_response(response, step[CONFIG_KEY_VALIDATE])

                # Update context counters
                context[CONTEXT_TPA_CALLS] += 1
                context[CONTEXT_TPA_TIME] += execution_time

                logger.info(f"GenericWorkflowExecutor._execute_step: Step {step_name} completed successfully on attempt {attempt + 1}")
                return {
                    RESULT_SUCCESS: True,
                    RESULT_TIME_MS: execution_time,
                    RESULT_DATA: response,
                    RESULT_EXTRACTED_DATA: extracted_data,
                    RESULT_ATTEMPT: attempt + 1
                }

            except Exception as e:
                logger.warning(f"GenericWorkflowExecutor._execute_step: Step {step_name} attempt {attempt + 1} failed: {str(e)}")
                if attempt == max_retries - 1:
                    # Last attempt failed
                    logger.error(f"GenericWorkflowExecutor._execute_step: Step {step_name} failed after {max_retries} attempts: {str(e)}")
                    return {
                        RESULT_SUCCESS: False,
                        RESULT_ERROR: str(e),
                        RESULT_ATTEMPT: attempt + 1
                    }
                else:
                    # Wait before retry
                    retry_delay = step.get(CONFIG_KEY_RETRY_DELAY_SECONDS, DEFAULT_RETRY_DELAY_SECONDS)
                    backoff_delay = retry_delay * (2 ** attempt)
                    logger.debug(f"GenericWorkflowExecutor._execute_step: Waiting {backoff_delay}s before retry for step {step_name}")
                    time.sleep(backoff_delay)  # Exponential backoff

        # This should never be reached, but just in case
        logger.error(f"GenericWorkflowExecutor._execute_step: Step {step_name} exceeded maximum retries")
        return {
            RESULT_SUCCESS: False,
            RESULT_ERROR: ERROR_MAX_RETRIES_EXCEEDED,
            RESULT_ATTEMPT: max_retries
        }

    def _execute_get_bearer_token(self, step: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """Execute get bearer token step"""
        request_data = WorkflowTemplateResolver.resolve_request_data(step[CONFIG_KEY_REQUEST], context[CONTEXT_STEP_DATA])
        headers = WorkflowTemplateResolver.resolve_headers(step.get(CONFIG_KEY_HEADERS, {}), context[CONTEXT_STEP_DATA])

        return self.tpa_service.make_dynamic_request(
            step[CONFIG_KEY_ENDPOINT],
            step[CONFIG_KEY_METHOD],
            request_data,
            headers
        )

    def _execute_verify_citizen_id(self, step: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """Execute verify citizen ID step"""
        request_data = WorkflowTemplateResolver.resolve_request_data(step[CONFIG_KEY_REQUEST], context[CONTEXT_STEP_DATA])
        headers = WorkflowTemplateResolver.resolve_headers(step.get(CONFIG_KEY_HEADERS, {}), context[CONTEXT_STEP_DATA])

        return self.tpa_service.make_dynamic_request(
            step[CONFIG_KEY_ENDPOINT],
            step[CONFIG_KEY_METHOD],
            request_data,
            headers
        )

    def _execute_verify_registration(self, step: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """Execute verify registration step"""
        request_data = WorkflowTemplateResolver.resolve_request_data(step[CONFIG_KEY_REQUEST], context[CONTEXT_STEP_DATA])
        headers = WorkflowTemplateResolver.resolve_headers(step.get(CONFIG_KEY_HEADERS, {}), context[CONTEXT_STEP_DATA])

        return self.tpa_service.make_dynamic_request(
            step[CONFIG_KEY_ENDPOINT],
            step[CONFIG_KEY_METHOD],
            request_data,
            headers
        )

    def _execute_fetch_policy_list(self, step: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """Execute fetch policy list step"""
        request_data = WorkflowTemplateResolver.resolve_request_data(step[CONFIG_KEY_REQUEST], context[CONTEXT_STEP_DATA])
        headers = WorkflowTemplateResolver.resolve_headers(step.get(CONFIG_KEY_HEADERS, {}), context[CONTEXT_STEP_DATA])

        return self.tpa_service.make_dynamic_request(
            step[CONFIG_KEY_ENDPOINT],
            step[CONFIG_KEY_METHOD],
            request_data,
            headers
        )

    def _execute_fetch_policy_details(self, step: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """Execute fetch policy details step"""
        request_data = WorkflowTemplateResolver.resolve_request_data(step[CONFIG_KEY_REQUEST], context[CONTEXT_STEP_DATA])
        headers = WorkflowTemplateResolver.resolve_headers(step.get(CONFIG_KEY_HEADERS, {}), context[CONTEXT_STEP_DATA])

        return self.tpa_service.make_dynamic_request(
            step[CONFIG_KEY_ENDPOINT],
            step[CONFIG_KEY_METHOD],
            request_data,
            headers
        )



class PolicyWorkflowService:
    """Service for executing policy workflows with caching and audit logging"""

    CACHE_DURATION_MINUTES = DEFAULT_CACHE_DURATION_MINUTES

    @classmethod
    def execute_policy_list_workflow_by_platform_id(cls, platform_id: int, user: User) -> Dict[str, Any]:
        """
        Execute policy list workflow using platformId parameter from frontend.
        This method handles the platformId lookup and extracts the required platform identity data.
        """
        logger.info(f"PolicyWorkflowService.execute_policy_list_workflow_by_platform_id: Starting workflow with platform_id={platform_id}, user={user.username}")

        # Get platform identity data using platformId
        platform_data = cls.get_platform_identity_data(platform_id)
        customer_id = platform_data['customer_id']

        logger.info(f"PolicyWorkflowService.execute_policy_list_workflow_by_platform_id: Resolved platform_id={platform_id} to customer_id={customer_id}, platform={platform_data['platform']}, user_id={platform_data['platform_user_id']}, channel_id={platform_data['channel_id']}")

        # Execute the workflow with the resolved customer_id and platform_id
        return cls.execute_policy_list_workflow(customer_id, platform_id, user)

    @classmethod
    def execute_policy_details_workflow_by_platform_id(cls, platform_id: int, member_code: str, user: User) -> Dict[str, Any]:
        """
        Execute policy details workflow using platformId parameter from frontend.
        This method handles the platformId lookup and extracts the required platform identity data.
        """
        logger.info(f"PolicyWorkflowService.execute_policy_details_workflow_by_platform_id: Starting workflow with platform_id={platform_id}, member_code={member_code}, user={user.username}")

        # Get platform identity data using platformId
        platform_data = cls.get_platform_identity_data(platform_id)
        customer_id = platform_data['customer_id']

        logger.info(f"PolicyWorkflowService.execute_policy_details_workflow_by_platform_id: Resolved platform_id={platform_id} to customer_id={customer_id}, platform={platform_data['platform']}, user_id={platform_data['platform_user_id']}, channel_id={platform_data['channel_id']}")

        # Execute the workflow with the resolved customer_id and platform_id
        return cls.execute_policy_details_workflow(customer_id, platform_id, member_code, user)
    
    @classmethod
    def execute_policy_list_workflow(cls, customer_id: int, platform_id: int, user: User) -> Dict[str, Any]:
        """Execute complete policy list workflow with caching"""
        execution_id = str(uuid.uuid4())
        start_time = time.time()

        logger.info(f"PolicyWorkflowService.execute_policy_list_workflow: Starting policy list workflow - customer_id={customer_id}, platform_id={platform_id}, user={user.username}, execution_id={execution_id}")

        # Validate input parameters
        if not customer_id or customer_id <= 0:
            raise PolicyWorkflowError("Invalid customer_id provided")
        if not platform_id or platform_id <= 0:
            raise PolicyWorkflowError("Invalid platform_id provided")

        try:
            # Get customer and platform identity
            logger.debug(f"PolicyWorkflowService.execute_policy_list_workflow: Retrieving customer and platform identity for customer_id={customer_id}, platform_id={platform_id}")
            customer = Customer.objects.get(customer_id=customer_id)
            platform_identity = cls._get_platform_identity_by_id(customer, platform_id)
            logger.debug(f"PolicyWorkflowService.execute_policy_list_workflow: Found platform identity - platform={platform_identity.platform}, user_id={platform_identity.platform_user_id}, channel_id={platform_identity.channel_id}")

            # Check cache first
            logger.debug(f"PolicyWorkflowService.execute_policy_list_workflow: Checking cache for customer {customer_id}, platform_id={platform_id}")
            cached_result = cls._get_cached_result(customer, WORKFLOW_TYPE_POLICY_LIST)
            if cached_result:
                logger.info(f"PolicyWorkflowService.execute_policy_list_workflow: Returning cached policy list for customer {customer_id}, platform_id={platform_id}, execution_id={cached_result.get(CONTEXT_EXECUTION_ID, 'unknown')}")
                return cached_result[CONTEXT_PROCESSED_DATA]

            logger.info(f"PolicyWorkflowService.execute_policy_list_workflow: No cache found, executing workflow for customer {customer_id}, platform_id={platform_id}")

            # Execute workflow using generic engine
            tpa_service = TPAApiService()
            workflow_executor = GenericWorkflowExecutor(tpa_service)

            context = {
                CONTEXT_CUSTOMER: customer,
                'customer_id': customer_id,
                'platform_identity': platform_identity,
                'platform_id': platform_id,
                CONTEXT_EXECUTION_ID: execution_id
            }

            execution_result = workflow_executor.execute_workflow(WORKFLOW_TYPE_POLICY_LIST, context)

            # Extract workflow data
            policy_list_data = execution_result[CONTEXT_STEP_DATA].get(DATA_KEY_POLICIES, [])
            raw_response = {FIELD_LIST_OF_POLICY_LIST_SOCIAL: policy_list_data}
            logger.debug(f"PolicyWorkflowService.execute_policy_list_workflow: Extracted {len(policy_list_data)} policies from workflow")

            # Process and format data
            logger.debug(f"PolicyWorkflowService.execute_policy_list_workflow: Processing policy list data")
            processed_data = cls._process_policy_list_data(raw_response)

            # Cache the result
            total_time = (time.time() - start_time) * 1000
            logger.debug(f"PolicyWorkflowService.execute_policy_list_workflow: Caching result for customer {customer_id}, platform_id={platform_id}")
            cls._cache_result(customer, WORKFLOW_TYPE_POLICY_LIST, None, platform_identity,
                            raw_response, processed_data, execution_id, total_time)

            # Log audit
            cls._log_audit(customer, user, WORKFLOW_TYPE_POLICY_LIST, execution_id, execution_result[CONTEXT_STEP_RESULTS],
                          total_time, True, None, execution_result[CONTEXT_TPA_CALLS], execution_result[CONTEXT_TPA_TIME])

            logger.info(f"PolicyWorkflowService.execute_policy_list_workflow: Successfully completed policy list workflow - customer_id={customer_id}, platform_id={platform_id}, execution_id={execution_id}, total_time={total_time:.2f}ms, policies_count={len(policy_list_data)}")
            return processed_data

        except Exception as e:
            total_time = (time.time() - start_time) * 1000
            error_details = {'error': str(e), 'type': type(e).__name__}

            # Log failed audit
            step_results = {}
            tpa_calls = 0
            tpa_time = 0
            try:
                cls._log_audit(customer, user, WORKFLOW_TYPE_POLICY_LIST, execution_id, step_results,
                              total_time, False, error_details, tpa_calls, tpa_time)
            except Exception as audit_error:
                logger.error(f"PolicyWorkflowService.execute_policy_list_workflow: Failed to log audit for failed workflow: {str(audit_error)}")

            logger.error(f"PolicyWorkflowService.execute_policy_list_workflow: Policy list workflow failed for customer {customer_id}, platform_id={platform_id}, execution_id={execution_id}: {str(e)}")
            raise PolicyWorkflowError(f"Policy list workflow failed: {str(e)}")
    
    @classmethod
    def execute_policy_details_workflow(cls, customer_id: int, platform_id: int, member_code: str, user: User) -> Dict[str, Any]:
        """Execute complete policy details workflow with caching"""
        execution_id = str(uuid.uuid4())
        start_time = time.time()

        logger.info(f"PolicyWorkflowService.execute_policy_details_workflow: Starting policy details workflow - customer_id={customer_id}, platform_id={platform_id}, member_code={member_code}, user={user.username}, execution_id={execution_id}")

        # Validate input parameters
        if not customer_id or customer_id <= 0:
            raise PolicyWorkflowError("Invalid customer_id provided")
        if not platform_id or platform_id <= 0:
            raise PolicyWorkflowError("Invalid platform_id provided")
        if not member_code or not member_code.strip():
            raise PolicyWorkflowError("Invalid member_code provided")

        try:
            # Get customer and platform identity
            logger.debug(f"PolicyWorkflowService.execute_policy_details_workflow: Retrieving customer and platform identity for customer_id={customer_id}, platform_id={platform_id}")
            customer = Customer.objects.get(customer_id=customer_id)
            platform_identity = cls._get_platform_identity_by_id(customer, platform_id)
            logger.debug(f"PolicyWorkflowService.execute_policy_details_workflow: Found platform identity - platform={platform_identity.platform}, user_id={platform_identity.platform_user_id}, channel_id={platform_identity.channel_id}")

            # Check cache first
            logger.debug(f"PolicyWorkflowService.execute_policy_details_workflow: Checking cache for customer {customer_id}, platform_id={platform_id}, member {member_code}")
            cached_result = cls._get_cached_result(customer, WORKFLOW_TYPE_POLICY_DETAILS, member_code)
            if cached_result:
                logger.info(f"PolicyWorkflowService.execute_policy_details_workflow: Returning cached policy details for customer {customer_id}, platform_id={platform_id}, member {member_code}, execution_id={cached_result.get(CONTEXT_EXECUTION_ID, 'unknown')}")
                return cached_result[CONTEXT_PROCESSED_DATA]

            logger.info(f"PolicyWorkflowService.execute_policy_details_workflow: No cache found, executing workflow for customer {customer_id}, platform_id={platform_id}, member {member_code}")

            # Execute workflow using generic engine
            tpa_service = TPAApiService()
            workflow_executor = GenericWorkflowExecutor(tpa_service)

            context = {
                CONTEXT_CUSTOMER: customer,
                'customer_id': customer_id,
                'platform_identity': platform_identity,
                'platform_id': platform_id,
                CONTEXT_EXECUTION_ID: execution_id,
                CONTEXT_MEMBER_CODE: member_code
            }

            execution_result = workflow_executor.execute_workflow(WORKFLOW_TYPE_POLICY_DETAILS, context)

            # Extract workflow data
            policy_details = execution_result[CONTEXT_STEP_DATA].get(DATA_KEY_POLICY_DETAILS, [])
            claims_data = execution_result[CONTEXT_STEP_DATA].get(DATA_KEY_CLAIMS_DATA, [])
            raw_response = {
                FIELD_LIST_OF_POL_DET: policy_details,
                FIELD_LIST_OF_POL_CLAIM: claims_data
            }
            logger.debug(f"PolicyWorkflowService.execute_policy_details_workflow: Extracted {len(policy_details)} policy details and {len(claims_data)} claims from workflow")

            # Process and format data
            logger.debug(f"PolicyWorkflowService.execute_policy_details_workflow: Processing policy details data")
            processed_data = cls._process_policy_details_data(raw_response, member_code)

            # Cache the result
            total_time = (time.time() - start_time) * 1000
            logger.debug(f"PolicyWorkflowService.execute_policy_details_workflow: Caching result for customer {customer_id}, platform_id={platform_id}, member {member_code}")
            cls._cache_result(customer, WORKFLOW_TYPE_POLICY_DETAILS, member_code, platform_identity,
                            raw_response, processed_data, execution_id, total_time)

            # Log audit
            cls._log_audit(customer, user, WORKFLOW_TYPE_POLICY_DETAILS, execution_id, execution_result[CONTEXT_STEP_RESULTS],
                          total_time, True, None, execution_result[CONTEXT_TPA_CALLS], execution_result[CONTEXT_TPA_TIME])

            logger.info(f"PolicyWorkflowService.execute_policy_details_workflow: Successfully completed policy details workflow - customer_id={customer_id}, platform_id={platform_id}, member_code={member_code}, execution_id={execution_id}, total_time={total_time:.2f}ms, policy_details_count={len(policy_details)}, claims_count={len(claims_data)}")
            return processed_data

        except Exception as e:
            total_time = (time.time() - start_time) * 1000
            error_details = {'error': str(e), 'type': type(e).__name__}

            # Log failed audit
            step_results = {}
            tpa_calls = 0
            tpa_time = 0
            try:
                cls._log_audit(customer, user, WORKFLOW_TYPE_POLICY_DETAILS, execution_id, step_results,
                              total_time, False, error_details, tpa_calls, tpa_time)
            except Exception as audit_error:
                logger.error(f"PolicyWorkflowService.execute_policy_details_workflow: Failed to log audit for failed workflow: {str(audit_error)}")

            logger.error(f"PolicyWorkflowService.execute_policy_details_workflow: Policy details workflow failed for customer {customer_id}, platform_id={platform_id}, member {member_code}, execution_id={execution_id}: {str(e)}")
            raise PolicyWorkflowError(f"Policy details workflow failed: {str(e)}")
    
    @classmethod
    def _get_platform_identity(cls, customer: Customer) -> CustomerPlatformIdentity:
        """Get platform identity for customer using default platform"""
        platform_identity = customer.get_identity_for_platform(DEFAULT_PLATFORM)
        if not platform_identity:
            raise CustomerDataError(f"No {DEFAULT_PLATFORM} platform identity found for customer {customer.customer_id}")
        return platform_identity

    @classmethod
    def get_platform_identity_data(cls, platform_id: int) -> Dict[str, Any]:
        """
        Get platform identity data by platformId parameter from frontend.
        Queries customer_customerplatformidentity table and extracts:
        - platform_user_id
        - channel_id
        - platform
        """
        logger.debug(f"PolicyWorkflowService.get_platform_identity_data: Looking up platform_id={platform_id}")

        try:
            platform_identity = CustomerPlatformIdentity.objects.get(
                id=platform_id,
                is_active=True
            )

            result = {
                'platform_user_id': platform_identity.platform_user_id,
                'channel_id': platform_identity.channel_id,
                'platform': platform_identity.platform,
                'customer_id': platform_identity.customer.customer_id,
                'display_name': platform_identity.display_name,
                'provider_id': platform_identity.provider_id,
                'provider_name': platform_identity.provider_name,
                'channel_name': platform_identity.channel_name
            }

            logger.info(f"PolicyWorkflowService.get_platform_identity_data: Found platform identity - platform={result['platform']}, user_id={result['platform_user_id']}, channel_id={result['channel_id']}, customer_id={result['customer_id']}")
            return result

        except CustomerPlatformIdentity.DoesNotExist:
            logger.error(f"PolicyWorkflowService.get_platform_identity_data: Platform identity with id={platform_id} not found")
            raise CustomerDataError(f"Platform identity with id={platform_id} not found")
        except Exception as e:
            logger.error(f"PolicyWorkflowService.get_platform_identity_data: Error retrieving platform identity: {str(e)}")
            raise CustomerDataError(f"Error retrieving platform identity: {str(e)}")

    @classmethod
    def _get_platform_identity_by_id(cls, customer: Customer, platform_id: int) -> CustomerPlatformIdentity:
        """Get specific platform identity by ID for customer"""
        logger.debug(f"PolicyWorkflowService._get_platform_identity_by_id: Looking for platform_id={platform_id} for customer_id={customer.customer_id}")

        try:
            platform_identity = CustomerPlatformIdentity.objects.get(
                id=platform_id,
                customer=customer,
                is_active=True
            )
            logger.debug(f"PolicyWorkflowService._get_platform_identity_by_id: Found platform identity - platform={platform_identity.platform}, user_id={platform_identity.platform_user_id}")
            return platform_identity
        except CustomerPlatformIdentity.DoesNotExist:
            logger.error(f"PolicyWorkflowService._get_platform_identity_by_id: Platform identity with id={platform_id} not found for customer {customer.customer_id}")
            raise CustomerDataError(f"Platform identity with id={platform_id} not found for customer {customer.customer_id}")
        except Exception as e:
            logger.error(f"PolicyWorkflowService._get_platform_identity_by_id: Error retrieving platform identity: {str(e)}")
            raise CustomerDataError(f"Error retrieving platform identity: {str(e)}")

    @classmethod
    def _get_cached_result(cls, customer: Customer, workflow_type: str, member_code: Optional[str] = None) -> Optional[Dict]:
        """Get cached workflow result if available and not expired"""
        logger.debug(f"PolicyWorkflowService._get_cached_result: Checking cache for customer_id={customer.customer_id}, workflow_type={workflow_type}, member_code={member_code}")

        try:
            cache_entry = CustomerPolicyWorkflowCache.objects.get(
                customer=customer,
                workflow_type=workflow_type,
                member_code=member_code,
                expires_at__gt=timezone.now()
            )
            logger.info(f"PolicyWorkflowService._get_cached_result: Found valid cache entry for customer_id={customer.customer_id}, workflow_type={workflow_type}, member_code={member_code}, execution_id={cache_entry.execution_id}")
            return {
                CONTEXT_PROCESSED_DATA: cache_entry.processed_data,
                CONTEXT_EXECUTION_ID: cache_entry.execution_id
            }
        except CustomerPolicyWorkflowCache.DoesNotExist:
            logger.debug(f"PolicyWorkflowService._get_cached_result: No valid cache entry found for customer_id={customer.customer_id}, workflow_type={workflow_type}, member_code={member_code}")
            return None

    @classmethod
    def _cache_result(cls, customer: Customer, workflow_type: str, member_code: Optional[str],
                     platform_identity: CustomerPlatformIdentity, raw_data: Dict, processed_data: Dict,
                     execution_id: str, execution_time: float):
        """Cache workflow result"""
        expires_at = timezone.now() + timedelta(minutes=cls.CACHE_DURATION_MINUTES)
        logger.debug(f"PolicyWorkflowService._cache_result: Caching result for customer_id={customer.customer_id}, workflow_type={workflow_type}, member_code={member_code}, execution_id={execution_id}, expires_at={expires_at}")

        with transaction.atomic():
            # Delete existing cache entry if exists
            deleted_count, _ = CustomerPolicyWorkflowCache.objects.filter(
                customer=customer,
                workflow_type=workflow_type,
                member_code=member_code
            ).delete()

            if deleted_count > 0:
                logger.debug(f"PolicyWorkflowService._cache_result: Deleted {deleted_count} existing cache entries")

            # Create new cache entry
            CustomerPolicyWorkflowCache.objects.create(
                customer=customer,
                workflow_type=workflow_type,
                member_code=member_code,
                citizen_id=customer.national_id,
                social_id=platform_identity.platform_user_id,
                channel_id=platform_identity.channel_id,
                channel=platform_identity.platform,
                raw_response_data=raw_data,
                processed_data=processed_data,
                execution_id=execution_id,
                execution_time_ms=int(execution_time),
                success=True,
                expires_at=expires_at
            )
            logger.info(f"PolicyWorkflowService._cache_result: Successfully cached result for customer_id={customer.customer_id}, workflow_type={workflow_type}, member_code={member_code}, execution_id={execution_id}")

    @classmethod
    def _log_audit(cls, customer: Customer, user: User, workflow_type: str, execution_id: str,
                  step_results: Dict, total_time: float, success: bool, error_details: Optional[Dict],
                  tpa_calls: int, tpa_time: float):
        """Log workflow execution audit"""
        CustomerPolicyWorkflowAuditLog.objects.create(
            customer=customer,
            requested_by=user,
            workflow_type=workflow_type,
            execution_id=execution_id,
            step_results=step_results,
            total_execution_time_ms=int(total_time),
            success=success,
            error_details=error_details,
            tpa_calls_made=tpa_calls,
            tpa_total_time_ms=int(tpa_time)
        )

    @classmethod
    def _process_policy_list_data(cls, raw_data: Dict) -> Dict[str, Any]:
        """Process and format policy list data for frontend"""
        policy_list = raw_data.get(FIELD_LIST_OF_POLICY_LIST_SOCIAL, [])

        # Extract member codes
        member_codes = []
        for policy in policy_list:
            member_code = policy.get(FIELD_MEMBER_CODE)
            if member_code and member_code not in member_codes:
                member_codes.append(member_code)

        return {
            'policy_list_data': raw_data,
            'member_codes': member_codes,
            'execution_metadata': {
                'total_policies': len(policy_list),
                'unique_member_codes': len(member_codes),
                'processed_at': timezone.now().isoformat()
            }
        }

    @classmethod
    def _process_policy_details_data(cls, raw_data: Dict, member_code: str) -> Dict[str, Any]:
        """Process and format policy details data for frontend"""
        policy_details = raw_data.get(FIELD_LIST_OF_POL_DET, [])
        policy_claims = raw_data.get(FIELD_LIST_OF_POL_CLAIM, [])

        return {
            'policy_details_data': raw_data,
            CONTEXT_MEMBER_CODE: member_code,
            'execution_metadata': {
                'total_policy_details': len(policy_details),
                'total_claims': len(policy_claims),
                'processed_at': timezone.now().isoformat()
            }
        }
