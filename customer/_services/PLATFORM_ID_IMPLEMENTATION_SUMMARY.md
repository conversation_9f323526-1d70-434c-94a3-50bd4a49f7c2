# Platform ID Implementation Summary

## Overview
Updated the `policy_workflow_service.py` file to implement data retrieval logic based on the JSON workflow configurations in `policy-list-workflow.json` and `policy-details-workflow.json`. The implementation now properly handles `platformId` parameters from the frontend and queries the `customer_customerplatformidentity` database table to extract the required platform identity data.

## Changes Made

### 1. Enhanced Database Query Method
**File**: `customer/_services/policy_workflow_service.py`
**Method**: `_execute_database_query`

- **Before**: Only accepted `customer_id` parameter
- **After**: Now accepts both `customer_id` and `platform_id` parameters
- **Improvement**: Properly handles both `:customer_id` and `:platform_id` placeholders in SQL queries
- **Added**: Better parameter validation and logging

```python
def _execute_database_query(self, db_config: Dict[str, Any], customer_id: str, platform_id: Optional[str] = None) -> Dict[str, Any]:
    # Enhanced to handle both customer_id and platform_id parameters
    # Validates required parameters based on SQL query requirements
    # Provides detailed logging for debugging
```

### 2. Updated Data Source Resolution
**Method**: `_resolve_data_source`

- **Enhancement**: Now extracts `platform_id` from the execution context
- **Integration**: Passes `platform_id` to database queries when available
- **Logging**: Added debug logging for resolved database data

### 3. Enhanced Workflow Execution Methods
**Methods**: `execute_policy_list_workflow` and `execute_policy_details_workflow`

- **Added**: `customer_id` and `platform_id` to execution context
- **Improvement**: Better logging with platform identity details including `channel_id`
- **Integration**: Ensures platform identity data is available for database queries

### 4. New Platform Identity Lookup Method
**Method**: `get_platform_identity_data`

- **Purpose**: Handles `platformId` parameter from frontend
- **Functionality**: Queries `customer_customerplatformidentity` table
- **Returns**: Dictionary containing all required fields:
  - `platform_user_id`
  - `channel_id`
  - `platform`
  - `customer_id`
  - `display_name`
  - `provider_id`
  - `provider_name`
  - `channel_name`

### 5. New Convenience Methods
**Methods**: 
- `execute_policy_list_workflow_by_platform_id`
- `execute_policy_details_workflow_by_platform_id`

- **Purpose**: Provide direct workflow execution using only `platformId`
- **Workflow**: 
  1. Accept `platformId` from frontend
  2. Query platform identity data
  3. Extract `customer_id`
  4. Execute appropriate workflow
- **Benefits**: Simplifies frontend integration

## JSON Workflow Configuration Integration

### Database Configuration Support
Both JSON workflow files already contain the correct database configuration:

```json
{
  "config": {
    "mode": "database",
    "database": [{
      "table": "customer_customerplatformidentity",
      "fields": {
        "social_id": "platform_user_id",
        "channel_id": "channel_id",
        "channel": "platform"
      },
      "where": "id = :platform_id"
    }]
  }
}
```

### Data Flow
1. **Frontend** sends `platformId` parameter
2. **Service** queries `customer_customerplatformidentity` table using `id = :platform_id`
3. **Extraction** retrieves `platform_user_id`, `channel_id`, and `platform` fields
4. **Workflow** uses extracted data for TPA API calls

## Usage Examples

### Direct Platform ID Lookup
```python
from customer._services.policy_workflow_service import PolicyWorkflowService

# Get platform identity data by platformId
platform_data = PolicyWorkflowService.get_platform_identity_data(platform_id=123)
print(f"Platform: {platform_data['platform']}")
print(f"User ID: {platform_data['platform_user_id']}")
print(f"Channel ID: {platform_data['channel_id']}")
```

### Workflow Execution by Platform ID
```python
from django.contrib.auth.models import User

user = User.objects.get(username='admin')

# Execute policy list workflow using platformId
result = PolicyWorkflowService.execute_policy_list_workflow_by_platform_id(
    platform_id=123,
    user=user
)

# Execute policy details workflow using platformId
result = PolicyWorkflowService.execute_policy_details_workflow_by_platform_id(
    platform_id=123,
    member_code='MEMBER123',
    user=user
)
```

### Traditional Workflow Execution (Still Supported)
```python
# Traditional method still works
result = PolicyWorkflowService.execute_policy_list_workflow(
    customer_id=456,
    platform_id=123,
    user=user
)
```

## Database Schema Requirements

The implementation expects the `customer_customerplatformidentity` table to have these fields:
- `id` (primary key)
- `platform_user_id` (string)
- `channel_id` (string, nullable)
- `platform` (string)
- `customer_id` (foreign key to customer table)
- `is_active` (boolean)

## Error Handling

- **Invalid Platform ID**: Raises `CustomerDataError` if platform identity not found
- **Missing Parameters**: Validates required parameters and provides clear error messages
- **Database Errors**: Catches and logs database connection issues
- **Workflow Failures**: Maintains existing error handling and audit logging

## Testing

A test script has been created at `customer/_services/test_policy_workflow_platform_id.py` to verify:
- Platform identity lookup functionality
- Workflow execution by platform ID
- Error handling scenarios

## Backward Compatibility

All existing functionality remains unchanged:
- Original workflow execution methods still work
- Existing API contracts maintained
- No breaking changes to current implementations

## Benefits

1. **Frontend Integration**: Simplified API - frontend only needs to pass `platformId`
2. **Data Consistency**: Ensures platform identity data is correctly retrieved and used
3. **Workflow Compliance**: Follows the data flow patterns defined in JSON workflow files
4. **Maintainability**: Clear separation of concerns with dedicated lookup methods
5. **Debugging**: Enhanced logging for troubleshooting platform identity issues
