from django.urls import path, include
from . import views 
from ._views import CustomerPlatformTickets
from ._views import customerdetails_tickets_views

urlpatterns = [
    path('api/customers/', views.CustomerListCreateView.as_view(), name='customer-list'),
    path('api/customers/paginated/', views.CustomerListPaginatedView.as_view(), name='customer-list-paginated'),
    # Get a specific customer by ID (Customer' Profile)
    path('api/customers/<int:pk>/details/', views.CustomerRetrieveUpdateDeleteView.as_view(), name='customer-detail'),
    path('api/gender/', views.GenderListCreateView.as_view(), name='gender-list'),
    path('api/gender/<int:pk>/', views.GenderRetrieveUpdateDeleteView.as_view(), name='gender-detail'),
    path('api/interface/', views.InterfaceListCreateView.as_view(), name='interface-list'),
    path('api/interface/<int:pk>/', views.InterfaceRetrieveUpdateDeleteView.as_view(), name='interface-detail'),
    path('api/customer-notes/', views.CustomerNoteList.as_view(), name='customer-note-list'),
    path('api/customer-notes/<int:pk>/', views.CustomerNoteDetail.as_view(), name='customer-note-detail'),
    path('api/customers/<int:customer_id>/notes/', views.CustomerSpecificNotesView.as_view(), name='customer-specific-notes'),
    path('api/customers/<int:customer_id>/notes/<int:note_id>/', views.CustomerSpecificNotesView.as_view(), name='customer-specific-note-detail'),

    # path("get_customers/", views.GetCustomerView.as_view(), name='customer-get'),
    path("api/customers/<int:customer_id>/tickets/", customerdetails_tickets_views.CustomerTicketsView.as_view(), name='customer-tickets-list'),
    path('api/customers/<int:customer_id>/policies/', views.CustomerPoliciesView.as_view(), name='customer-policies'),
    path('api/customers/<int:customer_id>/messages/', views.CustomerConversationMessagesView.as_view(), name='customer-conversation-messages'),
    path('api/customers/<int:customer_id>/message-history/', views.CustomerMessageHistoryView.as_view(), name='customer-message-history'), # Pagination
    # CustomerTag CRUD endpoints
    path('api/customer-tag/', views.CustomerTagListCreateView.as_view(), name='customer-tag-list'),
    path('api/customer-tag/<int:pk>/', views.CustomerTagRetrieveUpdateDeleteView.as_view(), name='customer-tag-detail'),
    # Customer-Tag relationship endpoints
    path('api/customers/<int:customer_id>/tags/', views.CustomerTagsView.as_view(), name='customer-tags'),
    # Customer Memory endpoints
    path('api/customers/memories/', views.CustomerMemoryListCreateView.as_view(), name='customer-memory-list'),
    path('api/customers/memories/<int:pk>/', views.CustomerMemoryDetailView.as_view(), name='customer-memory-detail'),
    path('api/customers/<int:customer_id>/memories/', views.CustomerMemoriesView.as_view(), name='customer-memories'),
    path('api/ticket/<int:ticket_id>/extract-memories/', views.TriggerMemoryExtractionView.as_view(), name='extract-ticket-memories'),
    # Customer Ticket Analysis
    path('api/customers/<int:customer_id>/ticket-analyses/', views.CustomerTicketAnalysesView.as_view(), name='customer-ticket-analyses'),
    ### For Chat-Center page (v01) ###
    # # Customer List
    # path('api/customers/', views.CustomerChatCenterListView.as_view(), name='customer-detail'),
    # # Customer details
    # path('api/customers/<int:customer_id>/', views.CustomerDetailView.as_view(), name='customer-detail'),
    # # Platform identities
    # path('api/customers/<int:customer_id>/platform-identities/', views.CustomerPlatformIdentitiesView.as_view(), name='customer-platform-identities'),
    # # Linking endpoints
    # path('api/customers/<int:customer_id>/generate-linking-code/', views.GenerateLinkingCodeView.as_view(), name='generate-linking-code'),
    # path('api/customers/validate-linking-code/', views.ValidateLinkingCodeView.as_view(), name='validate-linking-code'),
    # path('api/customers/link-accounts/', views.LinkAccountsView.as_view(), name='link-accounts'),
    # path('api/customers/<int:customer_id>/unlink-platform/', views.UnlinkPlatformIdentityView.as_view(), name='unlink-platform'),
    # path('api/customers/<int:customer_id>/linking-history/', views.CustomerLinkingHistoryView.as_view(), name='customer-linking-history'),

    # # Platform message endpoints
    # path('api/customers/<int:customer_id>/platforms/<int:platform_id>/messages/', views.CustomerPlatformMessagesView.as_view(), name='customer-platform-messages'),
    # path('api/customers/<int:customer_id>/platforms/<int:platform_id>/send/', views.CustomerPlatformSendMessageView.as_view(), name='customer-platform-send'),
    # path('api/customers/<int:customer_id>/platforms/<int:platform_id>/mark-read/', views.CustomerPlatformMarkReadView.as_view(), name='customer-platform-mark-read'),
    
    # # Customer statistics
    # path('api/customers/<int:customer_id>/stats/', views.CustomerStatsView.as_view(), name='customer-stats'),

     ### For Chat-Center page (v02) ###
     # Customer list with activity
     path('api/customers/', views.CustomerListWithActivityView.as_view(), name='customer-list-activity'),
    
    # Customer detail with platforms
     path('api/customers/<int:customer_id>/', views.CustomerDetailWithPlatformsView.as_view(), name='customer-detail-platforms'),
    
     # Platform identities (paginated)
     path('api/customers/<int:customer_id>/platform-identities/', 
          views.CustomerPlatformIdentitiesView.as_view(), name='customer-platform-identities'),
          
     # Platform identity detail
     path('api/customers/<int:customer_id>/platform-identities/<int:platform_id>/', 
          views.CustomerPlatformIdentityDetailView.as_view(), name='customer-platform-identity-detail'),
     
     # # Platform tickets - get all tickets from the same platform identity
     # path('api/customers/<int:customer_id>/platforms/<int:platform_id>/tickets/', 
     #      CustomerPlatformTickets.CustomerPlatformTicketsView.as_view(), name='customer-platform-tickets'),
     
     # Initial ticket list by focus ticket
     path('api/customers/focus-ticket/<int:ticket_id>/initial-tickets/', 
          CustomerPlatformTickets.InitialTicketListByFocusTicketView.as_view(), name='initial-ticket-list-by-focus-ticket'),
     
     # Load more tickets by focus ticket
     path('api/customers/focus-ticket/<int:ticket_id>/more-tickets/', 
          CustomerPlatformTickets.LoadMoreTicketsByFocusTicketView.as_view(), name='load-more-tickets-by-focus-ticket'),
     
     # Latest messages per platform
     path('api/customers/<int:customer_id>/platform-messages/', 
          views.CustomerPlatformMessagesView.as_view(), name='customer-platform-messages'),
     
     # Unread counts per platform
     path('api/customers/<int:customer_id>/unread-counts/', 
          views.CustomerUnreadCountsView.as_view(), name='customer-unread-counts'),
     
     # Platform conversation (Get messsages, Send a message)
     path('api/customers/<int:customer_id>/platforms/<int:platform_id>/messages/',
          views.PlatformConversationView.as_view(), name='platform-conversation'),

     # File (attachment) upload/delete endpoints
     path('api/customers/<int:customer_id>/platforms/<int:platform_id>/upload-files/',
          views.PlatformFileUploadView.as_view(), name='platform-file-upload'),
     path('api/customers/<int:customer_id>/platforms/<int:platform_id>/files/',
          views.PlatformFileDeleteView.as_view(), name='platform-file-delete'),

     # Mark messages as read
     path('api/customers/<int:customer_id>/platforms/<int:platform_id>/read/',
         views.MarkMessagesAsReadView.as_view(), name='mark-messages-read'),
     
     # Mark all my messages as read
     path('api/customers/<int:customer_id>/platforms/<int:platform_id>/readAll/', 
         views.MarkAllMessagesAsReadView.as_view(), name='mark-all-messages-read'),

     # Platform conversations for multiple tickets
     path('api/customers/<int:customer_id>/platforms/<int:platform_id>/conversations/', 
          CustomerPlatformTickets.PlatformConversationsView.as_view(), name='platform-conversations'),

     # CunstomerPlatformIdentity list
     path('api/platform-identities/', views.CustomerPlatformIdentityListView.as_view(), name='platform-identities-list'),

     path('api/platform-messages/', views.PlatformMessagesView.as_view(), name='platform-messages'),
     path('api/platform-unread-counts/', views.PlatformUnreadCountsView.as_view(), name='platform-unread-counts'),


    # Azure Blob for Customer table
    path('<int:customer_id>/files/', views.CustomerFileListView.as_view(), name='customer-file-list'),
    path('<int:customer_id>/files/upload/', views.CustomerFileUploadView.as_view(), name='customer-file-upload'),
    path('<int:customer_id>/files/delete/<str:filename>/', views.CustomerFileDeleteView.as_view(), name='customer-file-delete'),
    path('<int:customer_id>/files/download/<str:filename>/', views.CustomerFileDownloadView.as_view(), name='customer-file-download'),
    path('<int:customer_id>/files/download-bulk/', views.CustomerBulkFileDownloadView.as_view(), name='customer-bulk-file-download'),

    # Filter dropdown endpoints
    path('api/filters/tags/', views.CustomerTagListForFilterView.as_view(), name='customer-filter-tags'),
    path('api/filters/platforms/', views.CustomerPlatformListForFilterView.as_view(), name='customer-filter-platforms'),

    # Policy workflow endpoints
    path('api/customers/<int:customer_id>/platforms/<int:platform_id>/policies/workflow/',
         views.CustomerPolicyListWorkflowView.as_view(),
         name='customer-policy-list-workflow'),
    path('api/customers/<int:customer_id>/platforms/<int:platform_id>/policies/<str:member_code>/details/workflow/',
         views.CustomerPolicyDetailsWorkflowView.as_view(),
         name='customer-policy-details-workflow'),

]